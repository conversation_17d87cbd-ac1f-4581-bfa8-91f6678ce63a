[project]
name = "video-content-extractor-mcp"
version = "0.1.0"
description = "MCP server for video content extraction: audio extraction, video trimming, frame extraction, and scene detection"
readme = "README.md"
requires-python = ">=3.12"
dependencies = [
    "ffmpeg-python>=0.2.0",
    "mcp[cli]>=1.9.0",
]

[project.scripts]
video-content-extractor-mcp = "server:main"

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.hatch.build.targets.wheel]
packages = ["."]
