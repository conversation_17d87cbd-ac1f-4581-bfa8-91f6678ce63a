"""
Word脚注管理工具类 - 提供基础功能
"""
import os
from typing import <PERSON><PERSON>


def check_file_writeable(filepath: str) -> Tuple[bool, str]:
    """
    检查文件是否可写
    
    Args:
        filepath: 文件路径
        
    Returns:
        Tuple of (is_writeable, error_message)
    """
    # 如果文件不存在，检查目录是否可写
    if not os.path.exists(filepath):
        directory = os.path.dirname(filepath)
        # 如果没有指定目录，使用当前目录
        if directory == '':
            directory = '.'
        if not os.path.exists(directory):
            return False, f"目录 {directory} 不存在"
        if not os.access(directory, os.W_OK):
            return False, f"目录 {directory} 不可写"
        return True, ""
    
    # 如果文件存在，检查是否可写
    if not os.access(filepath, os.W_OK):
        return False, f"文件 {filepath} 不可写（权限被拒绝）"
    
    # 尝试打开文件进行写入，检查是否被锁定
    try:
        with open(filepath, 'a'):
            pass
        return True, ""
    except IOError as e:
        return False, f"文件 {filepath} 不可写: {str(e)}"
    except Exception as e:
        return False, f"检查文件权限时出现未知错误: {str(e)}"


def ensure_docx_extension(filename: str) -> str:
    """
    确保文件名具有.docx扩展名
    
    Args:
        filename: 要检查的文件名
        
    Returns:
        带有.docx扩展名的文件名
    """
    if not filename.endswith('.docx'):
        return filename + '.docx'
    return filename
