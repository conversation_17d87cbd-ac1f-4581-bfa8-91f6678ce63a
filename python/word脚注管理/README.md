# word脚注管理 MCP服务

Word脚注管理MCP服务 - 提供脚注和尾注相关功能

## 功能特性

### 🔧 工具列表 (9个)
- `add_footnote_to_document - 向特定段落添加脚注`
- `add_footnote_after_text - 在指定文本后添加脚注`
- `add_footnote_before_text - 在指定文本前添加脚注`
- `add_footnote_enhanced - 增强的脚注添加功能`
- `add_endnote_to_document - 向特定段落添加尾注`
- `customize_footnote_style - 自定义脚注样式`
- `delete_footnote_from_document - 删除文档中的脚注`
- `add_footnote_robust - 健壮的脚注添加`
- `validate_document_footnotes - 验证文档脚注`

## 安装和配置

### 本地安装
```bash
cd python/word脚注管理
pip install -e .
```

### Claude Desktop配置
```json
{
  "mcpServers": {
    "word-footnote-management": {
      "command": "python",
      "args": ["-m", "word_footnote_management.main"],
      "cwd": "/path/to/python/word脚注管理"
    }
  }
}
```

## 使用示例

根据具体工具功能使用相应的命令。
