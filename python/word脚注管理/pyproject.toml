[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "word-footnote-management-mcp"
version = "1.0.0"
description = "Word脚注管理MCP服务 - 提供脚注和尾注相关功能"
authors = [
    {name = "Word MCP Services", email = "<EMAIL>"}
]
readme = "README.md"
license = {text = "MIT"}
requires-python = ">=3.10"
dependencies = [
    "python-docx>=1.1.0",
    "fastmcp>=2.8.1",
    "lxml>=4.9.0",
]

[project.scripts]
word-footnote-management = "word_footnote_management.main:main"

[tool.setuptools.packages.find]
where = ["."]
include = ["word_footnote_management*"]
