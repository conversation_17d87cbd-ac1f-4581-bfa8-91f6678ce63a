#!/usr/bin/env python3
"""
Archive Compression & Extraction MCP Server

Entry point for the MCP server that provides both compression and extraction capabilities.
"""

import sys
import asyncio
from pathlib import Path

# Add src directory to Python path
src_dir = Path(__file__).parent / "src"
sys.path.insert(0, str(src_dir))

from src.main import main

if __name__ == "__main__":
    asyncio.run(main())
