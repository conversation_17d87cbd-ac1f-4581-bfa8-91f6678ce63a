# Word文档管理 MCP服务

专门用于Word文档基础管理操作的MCP服务，提供文档的创建、复制、信息获取等核心功能。

## 功能特性

### 📄 文档管理工具 (7个)
- `create_document` - 创建新的Word文档
- `copy_document` - 复制现有文档
- `get_document_info` - 获取文档基本信息和元数据
- `get_document_text` - 提取文档中的所有文本内容
- `get_document_outline` - 获取文档结构大纲
- `list_available_documents` - 列出指定目录中的所有Word文档
- `get_document_xml` - 获取文档的原始XML结构

## 安装和配置

### 本地安装
```bash
cd python/word文档管理
pip install -e .
```

### Claude Desktop配置
在Claude Desktop的配置文件中添加：

```json
{
  "mcpServers": {
    "word-document-management": {
      "command": "python",
      "args": ["-m", "word_document_management.main"],
      "cwd": "/path/to/python/word文档管理"
    }
  }
}
```

## 使用示例

- "创建一个名为'项目报告.docx'的新文档"
- "复制'模板.docx'文档为'新报告.docx'"
- "获取'报告.docx'文档的基本信息"
- "提取'合同.docx'中的所有文本内容"
- "显示'手册.docx'的文档结构大纲"
- "列出当前目录下的所有Word文档"

## API参考

### create_document
创建新的Word文档，可选择设置标题和作者信息。

### copy_document  
复制现有文档到新位置，支持自动命名。

### get_document_info
返回文档的详细信息，包括作者、创建时间、修改时间、页数等元数据。

### get_document_text
提取并返回文档中的所有文本内容，包括表格中的文本。

### get_document_outline
分析文档结构，返回标题层次和段落组织信息。

### list_available_documents
扫描指定目录，返回所有.docx文件的列表。

### get_document_xml
获取Word文档的底层XML结构，用于高级分析和调试。
