[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "word-document-management-mcp"
version = "1.0.0"
description = "Word文档管理MCP服务 - 提供文档创建、复制、信息获取等基础管理功能"
authors = [
    {name = "Word MCP Services", email = "<EMAIL>"}
]
readme = "README.md"
license = {text = "MIT"}
requires-python = ">=3.10"
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.8",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
]
dependencies = [
    "python-docx>=1.1.0",
    "fastmcp>=2.8.1",
    "lxml>=4.9.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.0.0",
    "pytest-asyncio>=0.21.0",
    "black>=23.0.0",
    "isort>=5.12.0",
    "flake8>=6.0.0",
]

[project.scripts]
word-document-management = "word_document_management.main:main"

[tool.setuptools.packages.find]
where = ["."]
include = ["word_document_management*"]

[tool.black]
line-length = 100
target-version = ['py38']

[tool.isort]
profile = "black"
line_length = 100
