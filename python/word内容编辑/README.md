# Word内容编辑 MCP服务

专门用于Word文档内容编辑操作的MCP服务，提供段落、标题、表格、图片等内容的添加和编辑功能。

## 功能特性

### ✏️ 内容编辑工具 (11个)
- `add_paragraph` - 添加段落到文档
- `add_heading` - 添加标题到文档
- `add_picture` - 添加图片到文档
- `add_table` - 添加表格到文档
- `add_page_break` - 添加分页符
- `delete_paragraph` - 删除指定段落
- `search_and_replace` - 查找并替换文本
- `insert_header_near_text` - 在指定文本附近插入标题
- `insert_line_or_paragraph_near_text` - 在指定文本附近插入行或段落
- `insert_numbered_list_near_text` - 在指定文本附近插入编号列表
- `replace_paragraph_block_below_header` - 替换标题下的段落块

## 安装和配置

### 本地安装
```bash
cd python/word内容编辑
pip install -e .
```

### Claude Desktop配置
在Claude Desktop的配置文件中添加：

```json
{
  "mcpServers": {
    "word-content-editing": {
      "command": "python",
      "args": ["-m", "word_content_editing.main"],
      "cwd": "/path/to/python/word内容编辑"
    }
  }
}
```

## 使用示例

- "在文档中添加一个段落：'这是新的段落内容'"
- "添加一级标题：'第一章 概述'"
- "插入图片 'chart.png' 到文档中"
- "创建一个3x4的表格并填入数据"
- "在文档中添加分页符"
- "删除第5个段落"
- "将文档中的'旧术语'替换为'新术语'"
- "在'重要提示'文本后插入二级标题"
- "在第3段后插入编号列表"

## API参考

### add_paragraph
向文档添加新段落，支持指定样式。

### add_heading
添加指定级别的标题（1-9级）。

### add_picture
插入图片文件，支持设置宽度和比例。

### add_table
创建指定行列数的表格，可选择填入初始数据。

### add_page_break
在文档当前位置插入分页符。

### delete_paragraph
根据索引删除指定段落。

### search_and_replace
在整个文档中查找并替换指定文本。

### insert_header_near_text
在指定文本附近插入标题，支持前后位置选择。

### insert_line_or_paragraph_near_text
在指定文本附近插入新的行或段落。

### insert_numbered_list_near_text
在指定文本附近插入编号列表。

### replace_paragraph_block_below_header
替换指定标题下的整个段落块。
