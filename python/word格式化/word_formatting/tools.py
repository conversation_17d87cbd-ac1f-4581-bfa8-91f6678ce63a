"""
Word格式化工具 - 提供样式创建、文本格式化、表格格式化等功能
"""
import os
from typing import List, Optional
from docx import Document
from docx.shared import Pt, RGBColor
from docx.enum.style import WD_STYLE_TYPE

from .utils import (
    check_file_writeable, 
    ensure_docx_extension,
    create_style,
    apply_table_style,
    apply_alternating_row_shading,
    highlight_header_row,
    set_cell_shading,
    set_cell_alignment,
    set_column_width
)


def create_custom_style(filename: str, style_name: str,
                             bold: Optional[bool] = None, italic: Optional[bool] = None,
                             font_size: Optional[int] = None, font_name: Optional[str] = None,
                             color: Optional[str] = None, base_style: Optional[str] = None) -> str:
    """
    在文档中创建自定义样式
    
    Args:
        filename: Word文档路径
        style_name: 新样式的名称
        bold: 设置文本加粗 (True/False)
        italic: 设置文本斜体 (True/False)
        font_size: 字体大小（磅）
        font_name: 字体名称/系列
        color: 文本颜色（例如：'red', 'blue'）
        base_style: 可选的现有样式作为基础
    """
    filename = ensure_docx_extension(filename)
    
    if not os.path.exists(filename):
        return f"文档 {filename} 不存在"
    
    # 检查文件是否可写
    is_writeable, error_message = check_file_writeable(filename)
    if not is_writeable:
        return f"无法修改文档: {error_message}。请考虑先创建副本。"
    
    try:
        doc = Document(filename)
        
        # 构建字体属性字典
        font_properties = {}
        if bold is not None:
            font_properties['bold'] = bold
        if italic is not None:
            font_properties['italic'] = italic
        if font_size is not None:
            font_properties['size'] = font_size
        if font_name is not None:
            font_properties['name'] = font_name
        if color is not None:
            font_properties['color'] = color
        
        # 创建样式
        new_style = create_style(
            doc, 
            style_name, 
            WD_STYLE_TYPE.PARAGRAPH, 
            base_style=base_style,
            font_properties=font_properties
        )
        
        doc.save(filename)
        return f"样式 '{style_name}' 创建成功。"
    except Exception as e:
        return f"创建样式失败: {str(e)}"


def format_text(filename: str, paragraph_index: int, start_pos: int, end_pos: int,
                     bold: Optional[bool] = None, italic: Optional[bool] = None,
                     underline: Optional[bool] = None, color: Optional[str] = None,
                     font_size: Optional[int] = None, font_name: Optional[str] = None) -> str:
    """
    格式化段落中的特定文本范围
    
    Args:
        filename: Word文档路径
        paragraph_index: 段落索引（从0开始）
        start_pos: 段落文本中的开始位置
        end_pos: 段落文本中的结束位置
        bold: 设置文本加粗 (True/False)
        italic: 设置文本斜体 (True/False)
        underline: 设置文本下划线 (True/False)
        color: 文本颜色（例如：'red', 'blue'等）
        font_size: 字体大小（磅）
        font_name: 字体名称/系列
    """
    filename = ensure_docx_extension(filename)
    
    if not os.path.exists(filename):
        return f"文档 {filename} 不存在"
    
    # 检查文件是否可写
    is_writeable, error_message = check_file_writeable(filename)
    if not is_writeable:
        return f"无法修改文档: {error_message}。请考虑先创建副本。"
    
    try:
        doc = Document(filename)
        
        # 验证段落索引
        if paragraph_index < 0 or paragraph_index >= len(doc.paragraphs):
            return f"无效的段落索引。文档有 {len(doc.paragraphs)} 个段落 (0-{len(doc.paragraphs)-1})。"
        
        paragraph = doc.paragraphs[paragraph_index]
        text = paragraph.text
        
        # 验证位置
        if start_pos < 0 or end_pos > len(text) or start_pos >= end_pos:
            return f"无效的文本位置。段落长度: {len(text)}, 请求范围: {start_pos}-{end_pos}"
        
        target_text = text[start_pos:end_pos]
        
        # 清除现有运行并重新创建
        paragraph.clear()
        
        # 添加目标前的文本
        if start_pos > 0:
            paragraph.add_run(text[:start_pos])
        
        # 添加格式化的目标文本
        run_target = paragraph.add_run(target_text)
        
        # 应用格式化
        if bold is not None:
            run_target.bold = bold
        if italic is not None:
            run_target.italic = italic
        if underline is not None:
            run_target.underline = underline
        
        # 处理颜色
        if color:
            # 定义常见RGB颜色
            color_map = {
                'red': RGBColor(255, 0, 0),
                'blue': RGBColor(0, 0, 255),
                'green': RGBColor(0, 128, 0),
                'yellow': RGBColor(255, 255, 0),
                'black': RGBColor(0, 0, 0),
                'gray': RGBColor(128, 128, 128),
                'white': RGBColor(255, 255, 255),
                'purple': RGBColor(128, 0, 128),
                'orange': RGBColor(255, 165, 0)
            }
            
            try:
                if color.lower() in color_map:
                    # 使用预定义的RGB颜色
                    run_target.font.color.rgb = color_map[color.lower()]
                else:
                    # 尝试按名称设置颜色
                    run_target.font.color.rgb = RGBColor.from_string(color)
            except Exception as e:
                # 如果所有方法都失败，默认为黑色
                run_target.font.color.rgb = RGBColor(0, 0, 0)
        
        if font_size:
            run_target.font.size = Pt(font_size)
        if font_name:
            run_target.font.name = font_name
        
        # 添加目标后的文本
        if end_pos < len(text):
            paragraph.add_run(text[end_pos:])
        
        doc.save(filename)
        return f"文本 '{target_text}' 在段落 {paragraph_index} 中格式化成功。"
    except Exception as e:
        return f"格式化文本失败: {str(e)}"


def format_table(filename: str, table_index: int,
                      has_header_row: Optional[bool] = None,
                      border_style: Optional[str] = None,
                      shading: Optional[List[List[str]]] = None) -> str:
    """
    使用边框、阴影和结构格式化表格
    
    Args:
        filename: Word文档路径
        table_index: 表格索引（从0开始）
        has_header_row: 如果为True，将第一行格式化为标题
        border_style: 边框样式 ('none', 'single', 'double', 'thick')
        shading: 单元格背景颜色的二维列表（按行和列）
    """
    filename = ensure_docx_extension(filename)
    
    if not os.path.exists(filename):
        return f"文档 {filename} 不存在"
    
    # 检查文件是否可写
    is_writeable, error_message = check_file_writeable(filename)
    if not is_writeable:
        return f"无法修改文档: {error_message}。请考虑先创建副本。"
    
    try:
        doc = Document(filename)
        
        # 验证表格索引
        if table_index < 0 or table_index >= len(doc.tables):
            return f"无效的表格索引。文档有 {len(doc.tables)} 个表格 (0-{len(doc.tables)-1})。"
        
        table = doc.tables[table_index]
        
        # 应用格式化
        success = apply_table_style(table, has_header_row or False, border_style, shading)
        
        if success:
            doc.save(filename)
            return f"索引 {table_index} 处的表格格式化成功。"
        else:
            return f"格式化索引 {table_index} 处的表格失败。"
    except Exception as e:
        return f"格式化表格失败: {str(e)}"


def set_table_cell_shading(filename: str, table_index: int, row_index: int,
                                col_index: int, fill_color: str, pattern: str = "clear") -> str:
    """
    为特定表格单元格应用阴影/填充
    
    Args:
        filename: Word文档路径
        table_index: 表格索引（从0开始）
        row_index: 单元格行索引（从0开始）
        col_index: 单元格列索引（从0开始）
        fill_color: 背景颜色（十六进制字符串如"FF0000"或颜色名如"red"）
        pattern: 阴影模式 ("clear", "solid", "pct10", "pct20"等)
    """
    filename = ensure_docx_extension(filename)
    
    # 确保数值参数是正确的类型
    try:
        table_index = int(table_index)
        row_index = int(row_index)
        col_index = int(col_index)
    except (ValueError, TypeError):
        return "无效参数: table_index, row_index, 和 col_index 必须是整数"
    
    if not os.path.exists(filename):
        return f"文档 {filename} 不存在"
    
    # 检查文件是否可写
    is_writeable, error_message = check_file_writeable(filename)
    if not is_writeable:
        return f"无法修改文档: {error_message}。请考虑先创建副本。"
    
    try:
        doc = Document(filename)
        
        # 验证表格索引
        if table_index < 0 or table_index >= len(doc.tables):
            return f"无效的表格索引。文档有 {len(doc.tables)} 个表格 (0-{len(doc.tables)-1})。"
        
        table = doc.tables[table_index]
        
        # 验证行和列索引
        if row_index < 0 or row_index >= len(table.rows):
            return f"无效的行索引。表格有 {len(table.rows)} 行 (0-{len(table.rows)-1})。"
        
        if col_index < 0 or col_index >= len(table.columns):
            return f"无效的列索引。表格有 {len(table.columns)} 列 (0-{len(table.columns)-1})。"
        
        cell = table.cell(row_index, col_index)
        
        # 应用单元格阴影
        set_cell_shading(cell, fill_color=fill_color, pattern=pattern)
        
        doc.save(filename)
        return f"单元格阴影应用成功到表格 {table_index}，单元格 ({row_index},{col_index})。"
    except Exception as e:
        return f"应用单元格阴影失败: {str(e)}"


def apply_table_alternating_rows(filename: str, table_index: int,
                                     color1: str = "FFFFFF", color2: str = "F2F2F2") -> str:
    """
    为表格应用交替行颜色以提高可读性

    Args:
        filename: Word文档路径
        table_index: 表格索引（从0开始）
        color1: 奇数行颜色（十六进制字符串，默认白色）
        color2: 偶数行颜色（十六进制字符串，默认浅灰色）
    """
    filename = ensure_docx_extension(filename)

    # 确保数值参数是正确的类型
    try:
        table_index = int(table_index)
    except (ValueError, TypeError):
        return "无效参数: table_index 必须是整数"

    if not os.path.exists(filename):
        return f"文档 {filename} 不存在"

    # 检查文件是否可写
    is_writeable, error_message = check_file_writeable(filename)
    if not is_writeable:
        return f"无法修改文档: {error_message}。请考虑先创建副本。"

    try:
        doc = Document(filename)

        # 验证表格索引
        if table_index < 0 or table_index >= len(doc.tables):
            return f"无效的表格索引。文档有 {len(doc.tables)} 个表格 (0-{len(doc.tables)-1})。"

        table = doc.tables[table_index]

        # 应用交替行阴影
        success = apply_alternating_row_shading(table, color1, color2)

        if success:
            doc.save(filename)
            return f"交替行颜色应用成功到表格 {table_index}。"
        else:
            return f"应用交替行颜色失败。"
    except Exception as e:
        return f"应用交替行颜色失败: {str(e)}"


def highlight_table_header(filename: str, table_index: int,
                               header_color: str = "4472C4", text_color: str = "FFFFFF") -> str:
    """
    为表格标题行应用特殊高亮

    Args:
        filename: Word文档路径
        table_index: 表格索引（从0开始）
        header_color: 标题背景颜色（十六进制字符串，默认蓝色）
        text_color: 标题文本颜色（十六进制字符串，默认白色）
    """
    filename = ensure_docx_extension(filename)

    # 确保数值参数是正确的类型
    try:
        table_index = int(table_index)
    except (ValueError, TypeError):
        return "无效参数: table_index 必须是整数"

    if not os.path.exists(filename):
        return f"文档 {filename} 不存在"

    # 检查文件是否可写
    is_writeable, error_message = check_file_writeable(filename)
    if not is_writeable:
        return f"无法修改文档: {error_message}。请考虑先创建副本。"

    try:
        doc = Document(filename)

        # 验证表格索引
        if table_index < 0 or table_index >= len(doc.tables):
            return f"无效的表格索引。文档有 {len(doc.tables)} 个表格 (0-{len(doc.tables)-1})。"

        table = doc.tables[table_index]

        # 应用标题高亮
        success = highlight_header_row(table, header_color, text_color)

        if success:
            doc.save(filename)
            return f"标题高亮应用成功到表格 {table_index}。"
        else:
            return f"应用标题高亮失败。"
    except Exception as e:
        return f"应用标题高亮失败: {str(e)}"


def set_table_cell_alignment(filename: str, table_index: int, row_index: int, col_index: int,
                                 horizontal: str = "left", vertical: str = "top") -> str:
    """
    设置特定表格单元格的文本对齐

    Args:
        filename: Word文档路径
        table_index: 表格索引（从0开始）
        row_index: 行索引（从0开始）
        col_index: 列索引（从0开始）
        horizontal: 水平对齐 ("left", "center", "right", "justify")
        vertical: 垂直对齐 ("top", "center", "bottom")
    """
    filename = ensure_docx_extension(filename)

    # 确保数值参数是正确的类型
    try:
        table_index = int(table_index)
        row_index = int(row_index)
        col_index = int(col_index)
    except (ValueError, TypeError):
        return "无效参数: table_index, row_index, 和 col_index 必须是整数"

    if not os.path.exists(filename):
        return f"文档 {filename} 不存在"

    # 检查文件是否可写
    is_writeable, error_message = check_file_writeable(filename)
    if not is_writeable:
        return f"无法修改文档: {error_message}。请考虑先创建副本。"

    try:
        doc = Document(filename)

        # 验证表格索引
        if table_index < 0 or table_index >= len(doc.tables):
            return f"无效的表格索引。文档有 {len(doc.tables)} 个表格 (0-{len(doc.tables)-1})。"

        table = doc.tables[table_index]

        # 验证行和列索引
        if row_index < 0 or row_index >= len(table.rows):
            return f"无效的行索引。表格有 {len(table.rows)} 行 (0-{len(table.rows)-1})。"

        if col_index < 0 or col_index >= len(table.columns):
            return f"无效的列索引。表格有 {len(table.columns)} 列 (0-{len(table.columns)-1})。"

        cell = table.cell(row_index, col_index)

        # 设置单元格对齐
        set_cell_alignment(cell, horizontal, vertical)

        doc.save(filename)
        return f"单元格对齐设置成功，表格 {table_index}，单元格 ({row_index},{col_index}) 设为 {horizontal}/{vertical}。"
    except Exception as e:
        return f"设置单元格对齐失败: {str(e)}"


def set_table_column_width(filename: str, table_index: int, col_index: int,
                                width: float, width_type: str = "points") -> str:
    """
    设置特定表格列的宽度

    Args:
        filename: Word文档路径
        table_index: 表格索引（从0开始）
        col_index: 列索引（从0开始）
        width: 宽度值
        width_type: 宽度类型 ("points", "inches", "cm", "percent", "auto")
    """
    filename = ensure_docx_extension(filename)

    # 确保数值参数是正确的类型
    try:
        table_index = int(table_index)
        col_index = int(col_index)
        if width_type.lower() != "auto":
            width = float(width)
    except (ValueError, TypeError):
        return "无效参数: table_index 和 col_index 必须是整数，width 必须是数字"

    if not os.path.exists(filename):
        return f"文档 {filename} 不存在"

    # 检查文件是否可写
    is_writeable, error_message = check_file_writeable(filename)
    if not is_writeable:
        return f"无法修改文档: {error_message}。请考虑先创建副本。"

    try:
        doc = Document(filename)

        # 验证表格索引
        if table_index < 0 or table_index >= len(doc.tables):
            return f"无效的表格索引。文档有 {len(doc.tables)} 个表格 (0-{len(doc.tables)-1})。"

        table = doc.tables[table_index]

        # 验证列索引
        if col_index < 0 or col_index >= len(table.columns):
            return f"无效的列索引。表格有 {len(table.columns)} 列 (0-{len(table.columns)-1})。"

        # 转换宽度和类型为Word格式
        if width_type.lower() == "points":
            word_width = width
            word_type = "dxa"
        elif width_type.lower() == "inches":
            word_width = width * 72  # 每英寸72磅
            word_type = "dxa"
        elif width_type.lower() == "cm":
            word_width = width * 28.35  # 每厘米约28.35磅
            word_type = "dxa"
        elif width_type.lower() == "percent":
            word_width = width
            word_type = "pct"
        else:  # auto
            word_width = 0
            word_type = "auto"

        # 设置列宽
        success = set_column_width(table, col_index, word_width, word_type)

        if success:
            doc.save(filename)
            return f"列宽设置成功，表格 {table_index}，列 {col_index} 设为 {width} {width_type}。"
        else:
            return f"设置列宽失败。"
    except Exception as e:
        return f"设置列宽失败: {str(e)}"
