"""
格式化工具类 - 提供文件操作和文档格式化的基础功能
"""
import os
from typing import Dict, List, Any, Tuple, Optional
from docx import Document
from docx.shared import Pt, RGBColor, Inches, Cm
from docx.enum.style import WD_STYLE_TYPE
from docx.enum.text import WD_ALIGN_PARAGRAPH
from docx.enum.table import WD_CELL_VERTICAL_ALIGNMENT
from docx.oxml.shared import OxmlElement, qn
from docx.oxml.ns import nsdecls
from docx.oxml import parse_xml


def check_file_writeable(filepath: str) -> Tuple[bool, str]:
    """
    检查文件是否可写
    
    Args:
        filepath: 文件路径
        
    Returns:
        Tuple of (is_writeable, error_message)
    """
    # 如果文件不存在，检查目录是否可写
    if not os.path.exists(filepath):
        directory = os.path.dirname(filepath)
        # 如果没有指定目录，使用当前目录
        if directory == '':
            directory = '.'
        if not os.path.exists(directory):
            return False, f"目录 {directory} 不存在"
        if not os.access(directory, os.W_OK):
            return False, f"目录 {directory} 不可写"
        return True, ""
    
    # 如果文件存在，检查是否可写
    if not os.access(filepath, os.W_OK):
        return False, f"文件 {filepath} 不可写（权限被拒绝）"
    
    # 尝试打开文件进行写入，检查是否被锁定
    try:
        with open(filepath, 'a'):
            pass
        return True, ""
    except IOError as e:
        return False, f"文件 {filepath} 不可写: {str(e)}"
    except Exception as e:
        return False, f"检查文件权限时出现未知错误: {str(e)}"


def ensure_docx_extension(filename: str) -> str:
    """
    确保文件名具有.docx扩展名
    
    Args:
        filename: 要检查的文件名
        
    Returns:
        带有.docx扩展名的文件名
    """
    if not filename.endswith('.docx'):
        return filename + '.docx'
    return filename


def create_style(doc, style_name, style_type, base_style=None, font_properties=None, paragraph_properties=None):
    """
    在文档中创建新样式
    
    Args:
        doc: Document对象
        style_name: 新样式的名称
        style_type: 样式类型 (WD_STYLE_TYPE)
        base_style: 可选的基础样式继承
        font_properties: 字体属性字典 (bold, italic, size, name, color)
        paragraph_properties: 段落属性字典 (alignment, spacing)
        
    Returns:
        创建的样式
    """
    try:
        # 检查样式是否已存在
        style = doc.styles.get_by_id(style_name, WD_STYLE_TYPE.PARAGRAPH)
        return style
    except:
        # 创建新样式
        new_style = doc.styles.add_style(style_name, style_type)
        
        # 设置基础样式
        if base_style:
            new_style.base_style = doc.styles[base_style]
        
        # 设置字体属性
        if font_properties:
            font = new_style.font
            if 'bold' in font_properties:
                font.bold = font_properties['bold']
            if 'italic' in font_properties:
                font.italic = font_properties['italic']
            if 'size' in font_properties:
                font.size = Pt(font_properties['size'])
            if 'name' in font_properties:
                font.name = font_properties['name']
            if 'color' in font_properties:
                # 定义常见RGB颜色
                color_map = {
                    'red': RGBColor(255, 0, 0),
                    'blue': RGBColor(0, 0, 255),
                    'green': RGBColor(0, 128, 0),
                    'yellow': RGBColor(255, 255, 0),
                    'black': RGBColor(0, 0, 0),
                    'gray': RGBColor(128, 128, 128),
                    'white': RGBColor(255, 255, 255),
                    'purple': RGBColor(128, 0, 128),
                    'orange': RGBColor(255, 165, 0)
                }
                
                color_value = font_properties['color']
                try:
                    # 处理字符串颜色名称
                    if isinstance(color_value, str) and color_value.lower() in color_map:
                        font.color.rgb = color_map[color_value.lower()]
                    # 处理RGBColor对象
                    elif hasattr(color_value, 'rgb'):
                        font.color.rgb = color_value
                    # 尝试解析为RGB字符串
                    elif isinstance(color_value, str):
                        font.color.rgb = RGBColor.from_string(color_value)
                    # 如果已经是RGB值则直接使用
                    else:
                        font.color.rgb = color_value
                except Exception as e:
                    # 如果所有方法都失败，回退到黑色
                    font.color.rgb = RGBColor(0, 0, 0)
        
        # 设置段落属性
        if paragraph_properties:
            if 'alignment' in paragraph_properties:
                new_style.paragraph_format.alignment = paragraph_properties['alignment']
            if 'spacing' in paragraph_properties:
                new_style.paragraph_format.line_spacing = paragraph_properties['spacing']
        
        return new_style


def set_cell_border(cell, **kwargs):
    """
    设置表格单元格边框
    
    Args:
        cell: 表格单元格
        **kwargs: 边框属性
    """
    tc = cell._tc
    tcPr = tc.get_or_add_tcPr()
    
    # 创建边框元素
    tcBorders = tcPr.find(qn('w:tcBorders'))
    if tcBorders is None:
        tcBorders = OxmlElement('w:tcBorders')
        tcPr.append(tcBorders)
    
    # 设置各边框
    for edge in ('top', 'left', 'bottom', 'right'):
        if kwargs.get(edge):
            edge_element = tcBorders.find(qn(f'w:{edge}'))
            if edge_element is None:
                edge_element = OxmlElement(f'w:{edge}')
                tcBorders.append(edge_element)
            
            edge_element.set(qn('w:val'), kwargs.get('val', 'single'))
            edge_element.set(qn('w:sz'), str(kwargs.get('sz', 4)))
            edge_element.set(qn('w:space'), str(kwargs.get('space', 0)))
            edge_element.set(qn('w:color'), kwargs.get('color', '000000'))


def set_cell_shading(cell, fill_color="FFFFFF", pattern="clear"):
    """
    设置表格单元格阴影/填充
    
    Args:
        cell: 表格单元格
        fill_color: 填充颜色（十六进制字符串）
        pattern: 阴影模式
    """
    tc = cell._tc
    tcPr = tc.get_or_add_tcPr()
    
    # 移除现有阴影
    shading = tcPr.find(qn('w:shd'))
    if shading is not None:
        tcPr.remove(shading)
    
    # 创建新阴影
    shading = OxmlElement('w:shd')
    shading.set(qn('w:val'), pattern)
    shading.set(qn('w:color'), 'auto')
    shading.set(qn('w:fill'), fill_color.upper())
    
    tcPr.append(shading)


def set_cell_alignment(cell, horizontal="left", vertical="top"):
    """
    设置表格单元格对齐方式
    
    Args:
        cell: 表格单元格
        horizontal: 水平对齐 ("left", "center", "right", "justify")
        vertical: 垂直对齐 ("top", "center", "bottom")
    """
    # 设置垂直对齐
    vertical_alignment_map = {
        "top": WD_CELL_VERTICAL_ALIGNMENT.TOP,
        "center": WD_CELL_VERTICAL_ALIGNMENT.CENTER,
        "bottom": WD_CELL_VERTICAL_ALIGNMENT.BOTTOM
    }
    
    if vertical.lower() in vertical_alignment_map:
        cell.vertical_alignment = vertical_alignment_map[vertical.lower()]
    
    # 设置水平对齐
    horizontal_alignment_map = {
        "left": WD_ALIGN_PARAGRAPH.LEFT,
        "center": WD_ALIGN_PARAGRAPH.CENTER,
        "right": WD_ALIGN_PARAGRAPH.RIGHT,
        "justify": WD_ALIGN_PARAGRAPH.JUSTIFY
    }
    
    if horizontal.lower() in horizontal_alignment_map:
        for paragraph in cell.paragraphs:
            paragraph.alignment = horizontal_alignment_map[horizontal.lower()]


def apply_table_style(table, has_header_row=False, border_style=None, shading=None):
    """
    为表格应用格式化

    Args:
        table: 要格式化的表格
        has_header_row: 如果为True，将第一行格式化为标题
        border_style: 边框样式 ('none', 'single', 'double', 'thick')
        shading: 单元格背景颜色的二维列表（按行和列）

    Returns:
        成功返回True，否则返回False
    """
    try:
        # 如果需要，格式化标题行
        if has_header_row and table.rows:
            header_row = table.rows[0]
            for cell in header_row.cells:
                for paragraph in cell.paragraphs:
                    if paragraph.runs:
                        for run in paragraph.runs:
                            run.bold = True

        # 如果指定了边框样式，应用边框样式
        if border_style:
            val_map = {
                'none': 'nil',
                'single': 'single',
                'double': 'double',
                'thick': 'thick'
            }
            val = val_map.get(border_style.lower(), 'single')

            # 应用到所有单元格
            for row in table.rows:
                for cell in row.cells:
                    set_cell_border(
                        cell,
                        top=True,
                        bottom=True,
                        left=True,
                        right=True,
                        val=val,
                        color="000000"
                    )

        # 如果提供了阴影，应用阴影
        if shading:
            for i, row in enumerate(table.rows):
                if i < len(shading):
                    for j, cell in enumerate(row.cells):
                        if j < len(shading[i]):
                            set_cell_shading(cell, fill_color=shading[i][j])

        return True
    except Exception as e:
        print(f"应用表格样式时出错: {e}")
        return False


def apply_alternating_row_shading(table, color1="FFFFFF", color2="F2F2F2"):
    """
    为表格应用交替行颜色以提高可读性

    Args:
        table: 要格式化的表格
        color1: 奇数行颜色（十六进制字符串）
        color2: 偶数行颜色（十六进制字符串）

    Returns:
        成功返回True，否则返回False
    """
    try:
        for i, row in enumerate(table.rows):
            fill_color = color1 if i % 2 == 0 else color2
            for cell in row.cells:
                set_cell_shading(cell, fill_color=fill_color)
        return True
    except Exception as e:
        print(f"应用交替行阴影时出错: {e}")
        return False


def highlight_header_row(table, header_color="4472C4", text_color="FFFFFF"):
    """
    为表格标题行应用特殊高亮

    Args:
        table: 要格式化的表格
        header_color: 标题背景颜色（十六进制字符串，默认蓝色）
        text_color: 标题文本颜色（十六进制字符串，默认白色）

    Returns:
        成功返回True，否则返回False
    """
    try:
        if not table.rows:
            return False

        header_row = table.rows[0]
        for cell in header_row.cells:
            # 设置背景颜色
            set_cell_shading(cell, fill_color=header_color)

            # 设置文本颜色和加粗
            for paragraph in cell.paragraphs:
                for run in paragraph.runs:
                    run.bold = True
                    try:
                        # 转换十六进制颜色为RGB
                        if len(text_color) == 6:
                            r = int(text_color[0:2], 16)
                            g = int(text_color[2:4], 16)
                            b = int(text_color[4:6], 16)
                            run.font.color.rgb = RGBColor(r, g, b)
                    except:
                        # 如果颜色转换失败，使用白色
                        run.font.color.rgb = RGBColor(255, 255, 255)

        return True
    except Exception as e:
        print(f"高亮标题行时出错: {e}")
        return False


def set_column_width(table, col_index, width, width_type="dxa"):
    """
    设置表格列宽

    Args:
        table: 要修改的表格
        col_index: 列索引
        width: 宽度值
        width_type: 宽度类型 ("dxa" 表示点*20, "pct" 表示百分比*50, "auto")

    Returns:
        成功返回True，否则返回False
    """
    try:
        if col_index >= len(table.columns):
            return False

        # 获取表格网格
        tbl = table._tbl
        tblGrid = tbl.find(qn('w:tblGrid'))
        if tblGrid is None:
            return False

        # 获取列元素
        gridCols = tblGrid.findall(qn('w:gridCol'))
        if col_index >= len(gridCols):
            return False

        gridCol = gridCols[col_index]

        if width_type.lower() == "auto":
            # 移除宽度属性以使用自动宽度
            if gridCol.get(qn('w:w')):
                del gridCol.attrib[qn('w:w')]
        else:
            # 设置宽度
            if width_type.lower() == "dxa":
                gridCol.set(qn('w:w'), str(int(width)))
            elif width_type.lower() == "pct":
                gridCol.set(qn('w:w'), str(int(width * 50)))  # 百分比需要乘以50

        return True
    except Exception as e:
        print(f"设置列宽时出错: {e}")
        return False
