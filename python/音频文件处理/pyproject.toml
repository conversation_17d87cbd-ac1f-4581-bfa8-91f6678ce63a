[project]
name = "audio-processor-mcp"
version = "0.1.0"
description = "MCP server for audio processing: format conversion, metadata editing, volume adjustment, and audio analysis"
readme = "README.md"
requires-python = ">=3.12"
dependencies = [
    "ffmpeg-python>=0.2.0",
    "mcp[cli]>=1.9.0",
]

[project.scripts]
audio-processor-mcp = "server:main"

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.hatch.build.targets.wheel]
packages = ["."]
