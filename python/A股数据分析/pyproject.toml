[build-system]
requires = ["setuptools>=45", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "ashare-mcp"
version = "0.2.0"
description = "A股市场数据分析 MCP 服务器 - 基于 Model Context Protocol 的 A 股市场数据分析工具"
readme = "README.md"
requires-python = ">=3.10"
authors = [
    {name = "A股数据分析团队", email = "<EMAIL>"}
]

keywords = ["mcp", "a股", "股票", "数据分析", "baostock", "model-context-protocol"]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Topic :: Office/Business :: Financial :: Investment",
    "Topic :: Scientific/Engineering :: Information Analysis",
]
dependencies = [
    "baostock>=0.8.9",
    "httpx>=0.28.1",
    "mcp[cli]>=1.2.0",
    "pandas>=2.2.3",
    "annotated-types>=0.7.0",
    "anyio>=4.9.0",
    "certifi>=2025.4.26",
    "click>=8.1.8",
    "colorama>=0.4.6",
    "h11>=0.16.0",
    "httpcore>=1.0.9",
    "httpx-sse>=0.4.0",
    "idna>=3.10",
    "markdown-it-py>=3.0.0",
    "mdurl>=0.1.2",
    "numpy>=2.2.5",
    "pydantic>=2.11.3",
    "pydantic-core>=2.33.1",
    "pydantic-settings>=2.9.1",
    "pygments>=2.19.1",
    "python-dateutil>=2.9.0",
    "python-dotenv>=1.1.0",
    "pytz>=2025.2",
    "rich>=14.0.0",
    "shellingham>=1.5.4",
    "six>=1.17.0",
    "sniffio>=1.3.1",
    "sse-starlette>=2.3.3",
    "starlette>=0.46.2",
    "tabulate>=0.9.0",
    "typer>=0.15.3",
    "typing-extensions>=4.13.2",
    "typing-inspection>=0.4.0",
    "tzdata>=2025.2",
    "uvicorn>=0.34.2",
]

[project.scripts]
ashare-mcp = "a_share_mcp.mcp_server:main"

[project.urls]
Homepage = "https://github.com/your-username/ashare-mcp"
Repository = "https://github.com/your-username/ashare-mcp"
Issues = "https://github.com/your-username/ashare-mcp/issues"

[tool.setuptools.packages.find]
where = ["src"]

[tool.setuptools]
license-files = []
