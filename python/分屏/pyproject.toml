[project]
name = "computer-split-screen-mcp"
version = "1.2.0"
description = "Model Context Protocol Server via Cross-Platform (Mac & Windows) Split Screen Functions"
readme = "README.md"
requires-python = ">=3.9"
authors = [
    {name = "Beta"}
]
license = {file = "LICENSE"}
keywords = ["mcp", "split-screen", "window-management", "tiling", "windows", "macos", "cross-platform", "desktop-productivity", "pywin32"]
dependencies = [
    "mcp>=0.1.0"
]

[project.optional-dependencies]
windows = ["pywin32>=306"]

[project.scripts]
computer-split-screen-mcp = "splitscreen_mcp.__main__:main"

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.hatch.build.targets.wheel]
packages = ["src/splitscreen_mcp"]

[tool.hatch.build.targets.sdist]
include = [
    "/src/splitscreen_mcp/**/*.py",
    "/README.md",
    "/LICENSE",
    "/pyproject.toml"
]
