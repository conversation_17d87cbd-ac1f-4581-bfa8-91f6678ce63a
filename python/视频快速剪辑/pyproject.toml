[project]
name = "video-editor-mcp"
version = "0.1.0"
description = "MCP server for advanced video editing: aspect ratio adjustment, subtitles, overlays, concatenation, speed changes, and transitions"
readme = "README.md"
requires-python = ">=3.12"
dependencies = [
    "ffmpeg-python>=0.2.0",
    "mcp[cli]>=1.9.0",
]

[project.scripts]
video-editor-mcp = "server:main"

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.hatch.build.targets.wheel]
packages = ["."]
