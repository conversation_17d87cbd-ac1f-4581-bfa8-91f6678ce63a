[project]
name = "mcp-image-cutout"
version = "0.1.4"
description = "MCP服务器：火山引擎图像编辑工具，提供显著性分割、背景移除等图像处理功能"
readme = "README.md"
requires-python = ">=3.10"
license = {text = "MIT"}
authors = [
    {name = "fengjinchao", email = "<EMAIL>"}
]
keywords = ["mcp", "image", "cutout", "volcengine", "ai", "computer-vision"]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Topic :: Software Development :: Libraries :: Python Modules",
    "Topic :: Scientific/Engineering :: Image Processing",
    "Topic :: Scientific/Engineering :: Artificial Intelligence",
]
dependencies = [
    "httpx[socks]>=0.28.1",
    "mcp[cli]>=1.12.0",
    "volcengine>=1.0.0",
    "pillow>=9.0.0",
]

[project.urls]
Homepage = "https://github.com/fengjinchao/mcp-image-cutout"
Repository = "https://github.com/fengjinchao/mcp-image-cutout"
Documentation = "https://github.com/fengjinchao/mcp-image-cutout#readme"
Issues = "https://github.com/fengjinchao/mcp-image-cutout/issues"

[project.scripts]
mcp-image-cutout = "mcp_image_cutout.server:main"

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.hatch.build.targets.wheel]
packages = ["mcp_image_cutout"]

[dependency-groups]
dev = [
    "build>=1.3.0",
    "twine>=6.1.0",
]