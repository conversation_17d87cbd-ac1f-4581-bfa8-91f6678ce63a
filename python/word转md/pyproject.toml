[project]
name = "word-to-markdown-mcp"
version = "0.1.0"
description = "Word转Markdown MCP服务器 - Python版本"
readme = "README.md"
requires-python = ">=3.11"
dependencies = [
    "mcp>=1.0.0",
    "markitdown[docx]>=0.0.1a3",
    "python-docx>=1.0.0",
]

[project.scripts]
word-to-markdown-mcp = "word_to_markdown_mcp.server:main"

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.uv]
dev-dependencies = []