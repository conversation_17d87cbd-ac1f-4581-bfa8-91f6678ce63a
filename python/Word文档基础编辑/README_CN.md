# Office-Word-MCP-Server（中文文档）

[![smithery badge](https://smithery.ai/badge/@GongRzhe/Office-Word-MCP-Server)](https://smithery.ai/server/@GongRzhe/Office-Word-MCP-Server)

一个基于 Model Context Protocol（MCP）的服务器，用于创建、读取与操作 Microsoft Word 文档。此服务器通过标准化接口向 AI 助手暴露文档能力，支持丰富的文档编辑、格式化与分析工作流。

<a href="https://glama.ai/mcp/servers/@GongRzhe/Office-Word-MCP-Server">
  <img width="380" height="200" src="https://glama.ai/mcp/servers/@GongRzhe/Office-Word-MCP-Server/badge" alt="Office Word Server MCP server" />
</a>

![](https://badge.mcpx.dev?type=server "MCP Server")

## 概述

Office-Word-MCP-Server 实现了 [Model Context Protocol](https://modelcontextprotocol.io/)，将 Word 文档相关操作封装为工具与资源，作为 AI 助手与 Word 文档之间的桥梁。支持文档创建、内容插入、富文本与表格格式化、注释提取、文档保护等能力。

其采用模块化架构，核心能力、工具与实用工具相互解耦，便于维护与扩展。

### 示例

#### Prompt（示例）

![image](https://github.com/user-attachments/assets/f49b0bcc-88b2-4509-bf50-995b9a40038c)

#### 输出

![image](https://github.com/user-attachments/assets/ff64385d-3822-4160-8cdf-f8a484ccc01a)

## 功能特性

### 文档管理

- 创建新文档并写入元数据
- 提取全文与分析文档结构
- 查看文档属性与统计信息
- 列出目录下可用文档
- 复制现有文档
- 合并多个文档为单一文档
- 将 Word 文档转换为 PDF

### 内容创建

- 添加不同级别的标题
- 插入段落并可选样式
- 创建自定义数据表格
- 插入按比例缩放的图片
- 插入分页符
- 添加脚注与尾注，并互相转换
- 自定义脚注与尾注样式
- 专业化的技术文档表格排版
- 为教程材料设计强调框（Callout）与格式化内容
- 构建适用于商务报表的结构化数据表，支持一致性样式

### 富文本格式

- 段落内局部加粗、斜体、下划线
- 更改文本颜色、字体名称与字号
- 应用自定义样式
- 全文查找与替换
- 表格单元格内的独立文本格式化
- 多组合格式增强可读性
- 字体族与字号自定义

### 表格格式化

- 表格边框与整体样式
- 表头行高亮与独立格式
- 单元格底纹与自定义边框
- 结构化表格增强可读性
- 单元格背景色（支持命名颜色与十六进制）
- 交替行底色（隔行换色）
- 更显著的表头行配色与文本色
- 单元格文本格式（粗体、斜体、下划线、颜色、字号、字体）
- 全面的颜色支持（命名色与十六进制）
- 单元格内边距独立控制（四边）
- 单元格水平与垂直对齐
- 单元格合并（横向、纵向、矩形区域）
- 列宽管理（点、百分比、自动适配）
- 自动适配列宽
- 专业的 Callout 表格支持（图标列与样式化内容）

### 高级文档操作

- 删除段落
- 创建自定义文档样式
- 在文档范围内应用一致性格式
- 精细化的文本范围格式控制
- 支持点与百分比单位的灵活内边距
- 清晰可读的表格展示（适当对齐与间距）

### 文档保护

- 添加密码保护
- 限制编辑模式并定义可编辑区域
- 添加与验证数字签名
- 校验文档真实性与完整性

### 注释提取

- 提取全部注释
- 按作者筛选注释
- 获取指定段落的注释
- 获取注释元信息（作者、日期、内容）

## 安装

### 通过 Smithery 安装

使用 [Smithery](https://smithery.ai/server/@GongRzhe/Office-Word-MCP-Server) 为 Claude 桌面版自动安装：

```bash
npx -y @smithery/cli install @GongRzhe/Office-Word-MCP-Server --client claude
```

### 先决条件

- Python 3.8 或更高版本
- pip 包管理器

### 基本安装（本地）

```bash
# 克隆仓库
git clone https://github.com/GongRzhe/Office-Word-MCP-Server.git
cd Office-Word-MCP-Server

# 安装依赖
pip install -r requirements.txt
```

### 使用一键安装脚本

也可以使用项目内的安装脚本，自动完成：

- 环境检查
- 创建虚拟环境
- 安装依赖
- 生成 MCP 配置

```bash
python setup_mcp.py
```

## 搭配 Claude 桌面版使用

### 配置

#### 方式一：本地安装后引用

在 Claude 桌面版的配置文件中加入：

```json
{
  "mcpServers": {
    "word-document-server": {
      "command": "python",
      "args": ["/path/to/word_mcp_server.py"]
    }
  }
}
```

#### 方式二：无需本地安装（使用 uvx）

直接通过 uvx 运行：

```json
{
  "mcpServers": {
    "word-document-server": {
      "command": "uvx",
      "args": ["--from", "office-word-mcp-server", "word_mcp_server"]
    }
  }
}
```

配置文件路径：

- macOS: `~/Library/Application Support/Claude/claude_desktop_config.json`
- Windows: `%APPDATA%\Claude\claude_desktop_config.json`

完成后重启 Claude 桌面版以载入配置。

### 典型操作示例

- “创建一个名为 ‘report.docx’ 的新文档并添加标题页”
- “在我的文档中添加一个标题与三段文字”
- “插入一个 4x4 的销售数据表格”
- “将第 2 段中的单词 ‘重要’ 加粗并标红”
- “把全文中的 ‘旧术语’ 替换为 ‘新术语’”
- “为章节标题创建一个自定义样式”
- “对文档中的表格进行格式化”
- “提取文档中的全部注释”
- “只显示 John Doe 的所有注释”
- “获取第 3 段的注释”
- “将表格单元格 (1,2) 的文本设为加粗、蓝色、字号 14pt”
- “将表头单元格的四边内边距设置为 10pt”
- “创建一张带蓝色对勾图标与白色文字的 Callout 表格”
- “设置第一列宽度为 50pt，并自动适配其余列宽”
- “对表格应用隔行换色以提升可读性”

## API 参考

### 文档创建与属性

```python
create_document(filename, title=None, author=None)
get_document_info(filename)
get_document_text(filename)
get_document_outline(filename)
list_available_documents(directory=".")
copy_document(source_filename, destination_filename=None)
convert_to_pdf(filename, output_filename=None)
```

### 内容添加

```python
add_heading(filename, text, level=1)
add_paragraph(filename, text, style=None)
add_table(filename, rows, cols, data=None)
add_picture(filename, image_path, width=None)
add_page_break(filename)
```

### 内容提取

```python
get_document_text(filename)
get_paragraph_text_from_document(filename, paragraph_index)
find_text_in_document(filename, text_to_find, match_case=True, whole_word=False)
```

### 文本格式化

```python
format_text(
    filename,
    paragraph_index,
    start_pos,
    end_pos,
    bold=None,
    italic=None,
    underline=None,
    color=None,
    font_size=None,
    font_name=None,
)
search_and_replace(filename, find_text, replace_text)
delete_paragraph(filename, paragraph_index)
create_custom_style(
    filename,
    style_name,
    bold=None,
    italic=None,
    font_size=None,
    font_name=None,
    color=None,
    base_style=None,
)
```

### 表格格式化

```python
format_table(
    filename,
    table_index,
    has_header_row=None,
    border_style=None,
    shading=None,
)
set_table_cell_shading(
    filename,
    table_index,
    row_index,
    col_index,
    fill_color,
    pattern="clear",
)
apply_table_alternating_rows(filename, table_index, color1="FFFFFF", color2="F2F2F2")
highlight_table_header(
    filename,
    table_index,
    header_color="4472C4",
    text_color="FFFFFF",
)

# 单元格合并
merge_table_cells(filename, table_index, start_row, start_col, end_row, end_col)
merge_table_cells_horizontal(filename, table_index, row_index, start_col, end_col)
merge_table_cells_vertical(filename, table_index, col_index, start_row, end_row)

# 单元格对齐
set_table_cell_alignment(
    filename,
    table_index,
    row_index,
    col_index,
    horizontal="left",
    vertical="top",
)
set_table_alignment_all(filename, table_index, horizontal="left", vertical="top")

# 单元格文本格式
format_table_cell_text(
    filename,
    table_index,
    row_index,
    col_index,
    text_content=None,
    bold=None,
    italic=None,
    underline=None,
    color=None,
    font_size=None,
    font_name=None,
)

# 单元格内边距
set_table_cell_padding(
    filename,
    table_index,
    row_index,
    col_index,
    top=None,
    bottom=None,
    left=None,
    right=None,
    unit="points",
)

# 列宽管理
set_table_column_width(filename, table_index, col_index, width, width_type="points")
set_table_column_widths(filename, table_index, widths, width_type="points")
set_table_width(filename, table_index, width, width_type="points")
auto_fit_table_columns(filename, table_index)
```

### 注释提取

```python
get_all_comments(filename)
get_comments_by_author(filename, author)
get_comments_for_paragraph(filename, paragraph_index)
```

## 故障排查

### 常见问题

1. 缺失样式（Styles）
   - 部分文档可能缺少标题或表格相关的标准样式
   - 服务器会尝试创建缺失样式或直接应用行内格式
   - 最佳实践是使用包含标准样式的模板

2. 权限问题
   - 确保对目标路径具有读写权限
   - 使用 `copy_document` 生成副本以编辑受限文档
   - 若失败，请检查文件所有权与权限

3. 图片插入问题
   - 使用图片的绝对路径
   - 确认图片格式（推荐 JPEG、PNG）
   - 检查图片大小与权限

4. 表格格式问题
   - 单元格索引错误：行列索引需在表格范围内（从 0 开始）
   - 颜色格式：使用不带 `#` 的十六进制（如红色 `FF0000`）或标准颜色名
   - 内边距单位：设置单元格内边距时明确指定 `points` 或 `percent`
   - 列宽冲突：自动适配可能会覆盖手动列宽设置
   - 文本格式继承：先设置内容，再应用单元格文本格式，效果更稳定

### 调试

启用详细日志：

```bash
export MCP_DEBUG=1  # Linux/macOS
set MCP_DEBUG=1     # Windows
```

## 参与贡献

欢迎以 Pull Request 的方式贡献代码：

1. Fork 本仓库
2. 创建功能分支（`git checkout -b feature/amazing-feature`）
3. 提交更改（`git commit -m 'Add some amazing feature'`）
4. 推送分支（`git push origin feature/amazing-feature`）
5. 发起 Pull Request

## 许可证

本项目基于 MIT License 发布，详见 `LICENSE` 文件。

## 致谢

- [Model Context Protocol](https://modelcontextprotocol.io/) —— 协议规范
- [python-docx](https://python-docx.readthedocs.io/) —— Word 文档操作
- [FastMCP](https://github.com/modelcontextprotocol/python-sdk) —— Python 版 MCP 实现

---

提示：本服务器会与您系统上的文档文件进行交互。请在 Claude 桌面版或其他 MCP 客户端中执行操作前，确认请求内容的安全与合理性。
